/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&family=Squada+One&display=swap');

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
}

body {
  line-height: 1.6;
  background-color: #fff;
}

/* Hero Section */
.hero {
  background-color: #1AC073; /* Green background */
  width: 1440px;   /* full navbar width */
  height: 80px;    /* navbar height */
  margin: 0 auto;  /* center hero section if viewport > 1440px */
  display: flex;
  align-items: center;
}

/* Navbar Layout */
.navbar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 146px;  /* left/right padding from figma */
}

/* Logo */
.logo {
  font-family: 'Squada One', cursive;
  font-size: 32px;
  font-weight: 400;
  color: #fff;
  line-height: 20px;
  letter-spacing: -0.02em;
}

/* Nav Links */
.nav-links {
  list-style: none;
  display: flex;
  gap: 40px; /* spacing between menu items */
}

.nav-links a {
  text-decoration: none;
  font-family: 'Roboto', sans-serif;
  font-size: 20px;
  color: #fff;
}

/* Icons */
.nav-icons {
  display: flex;
  gap: 20px; /* spacing between icons */
}

.icon {
  font-size: 22px;
  color: #fff;
  cursor: pointer;
}

/* Hero Content */
.hero-content {

  font-family: 'Roboto', sans-serif;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 80px 146px 0;
  flex: 1;
  background-color: #1AC073;
  height: 650px;  /* to align with image height */
}

/* Left side (text + search) */
.left-hero {
  max-width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  margin-top: 110px;               /* Shift upward slightly */
  color: #fff;
}

.left-hero h1 {
 
  font-weight: 600;
  font-size: 60px;
  line-height: 1.1;
  margin-bottom: 20px;
}

.left-hero p {
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
}

/* Search Bar */
.hero-search {
  display: flex;
}

.hero-search input {
  padding: 12px 16px;
  font-size: 16px;
  border: none;
  border-radius: 5px 0 0 5px;
  width: 300px;
}

.hero-search button {
  padding: 12px 24px;
  background-color: #F9B52A;
  color: #fff;
  font-size: 16px;
  border: none;
  border-radius: 0 5px 5px 0;
  cursor: pointer;
}

/* Right side (image) */
.right-hero img {
  margin-top: 50px;
  width: 500px;
  height: 525px;
  object-fit: contain; /* keeps image proportions clean */
  opacity: 1;
  transform: rotate(0deg);
}
