/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  color: white;
  text-decoration: none;
  transition: opacity 0.3s;
}

.nav-links a:hover {
  opacity: 0.8;
}

.nav-icons {
  display: flex;
  gap: 1rem;
}

.icon {
  cursor: pointer;
  padding: 0.5rem;
  transition: opacity 0.3s;
}

.icon:hover {
  opacity: 0.8;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.left-hero {
  flex: 1;
  max-width: 500px;
}

.left-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.left-hero p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-search {
  display: flex;
  background: white;
  border-radius: 50px;
  overflow: hidden;
  max-width: 400px;
}

.hero-search input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  outline: none;
  color: #333;
}

.hero-search button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  transition: background 0.3s;
}

.hero-search button:hover {
  background: #ff5252;
}

.right-hero {
  flex: 1;
  text-align: center;
}

.right-hero img {
  max-width: 100%;
  height: auto;
}

/* Home Kitchen Section */
.home-kitchen {
  padding: 4rem 0;
  background: #f8f9fa;
}

.home-kitchen h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-align: left;
  color: #333;
  font-weight: bold;
}

.pizza-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.pizza-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.pizza-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.pizza-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.pizza-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.pizza-card:hover .pizza-image img {
  transform: scale(1.05);
}

.discount-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #ff6b6b;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.pizza-info {
  padding: 1.5rem;
  position: relative;
}

.pizza-info h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.price {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stars {
  color: #ffc107;
  font-size: 0.9rem;
}

.time {
  color: #666;
  font-size: 0.9rem;
}

.add-btn {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ffc107;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s, transform 0.2s;
}

.add-btn:hover {
  background: #ffb300;
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    padding: 2rem 1rem;
  }
  
  .left-hero h1 {
    font-size: 2rem;
  }
  
  .navbar {
    padding: 1rem;
  }
  
  .nav-links {
    display: none;
  }
  
  .pizza-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .home-kitchen {
    padding: 2rem 0;
  }
  
  .home-kitchen h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .pizza-grid {
    grid-template-columns: 1fr;
  }
  
  .left-hero h1 {
    font-size: 1.8rem;
  }
  
  .hero-search {
    flex-direction: column;
    border-radius: 10px;
  }
  
  .hero-search input,
  .hero-search button {
    border-radius: 0;
  }
}
